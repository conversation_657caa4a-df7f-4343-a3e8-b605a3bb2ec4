// Hero Slider Module JavaScript
(function() {
    'use strict';

    // Function to load Splide.js from CDN
    function loadSplide(callback) {
        // Check if Splide is already loaded
        if (window.Splide) {
            callback();
            return;
        }

        // Load Splide CSS
        const cssLink = document.createElement('link');
        cssLink.rel = 'stylesheet';
        cssLink.href = 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css';
        document.head.appendChild(cssLink);

        // Load Splide JS
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js';
        script.onload = callback;
        script.onerror = function() {
            console.error('Failed to load Splide.js from CDN');
        };
        document.head.appendChild(script);
    }

    // Function to initialize hero sliders
    function initializeHeroSliders() {
        const heroSliders = document.querySelectorAll('.hero-slider');

        heroSliders.forEach(function(slider) {
            // Skip if already initialized
            if (slider.classList.contains('splide-initialized')) {
                return;
            }

            // Get the number of slides
            const slides = slider.querySelectorAll('.splide__slide');
            const slideCount = slides.length;

            // Only initialize if there are slides
            if (slideCount === 0) {
                return;
            }

            // Configure Splide options
            const splideOptions = {
                type: 'loop',
                autoplay: slideCount > 1, // Only autoplay if multiple slides
                interval: 5000, // 5 seconds
                pauseOnHover: true,
                pauseOnFocus: true,
                resetProgress: false,
                arrows: slideCount > 1, // Only show arrows if multiple slides
                pagination: slideCount > 1, // Only show pagination if multiple slides
                keyboard: true,
                drag: slideCount > 1, // Only allow dragging if multiple slides
                wheel: false,
                releaseWheel: false,
                waitForTransition: true,
                updateOnMove: true,
                trimSpace: false,
                focus: 'center',
                breakpoints: {
                    768: {
                        arrows: false, // Hide arrows on mobile
                        autoplay: false, // Disable autoplay on mobile for better UX
                    }
                }
            };

            try {
                // Initialize Splide
                const splide = new Splide(slider, splideOptions);

                // Handle video slides
                splide.on('active', function(slide) {
                    // Pause all videos first
                    const allVideos = slider.querySelectorAll('video, iframe');
                    allVideos.forEach(function(video) {
                        if (video.tagName === 'VIDEO') {
                            video.pause();
                        } else if (video.tagName === 'IFRAME') {
                            // For YouTube/Vimeo iframes, try to pause
                            try {
                                video.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}', '*');
                            } catch (e) {
                                // Ignore errors for cross-origin iframes
                            }
                        }
                    });

                    // Play video in active slide if it exists and is set to autoplay
                    const activeVideo = slide.slide.querySelector('video[autoplay], iframe[data-autoplay="true"]');
                    if (activeVideo && activeVideo.tagName === 'VIDEO') {
                        activeVideo.play().catch(function(error) {
                            console.log('Video autoplay prevented:', error);
                        });
                    }
                });

                // Mount the slider
                splide.mount();

                // Mark as initialized
                slider.classList.add('splide-initialized');

                console.log('Hero slider initialized successfully');

            } catch (error) {
                console.error('Error initializing hero slider:', error);
            }
        });
    }

    // Initialize when DOM is ready
    function init() {
        loadSplide(function() {
            initializeHeroSliders();

            // Re-initialize on dynamic content changes (for HubSpot preview mode)
            const observer = new MutationObserver(function(mutations) {
                let shouldReinit = false;
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1 && (node.classList.contains('hero-slider') || node.querySelector('.hero-slider'))) {
                                shouldReinit = true;
                            }
                        });
                    }
                });

                if (shouldReinit) {
                    setTimeout(initializeHeroSliders, 100);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        });
    }

    // Initialize based on document ready state
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
