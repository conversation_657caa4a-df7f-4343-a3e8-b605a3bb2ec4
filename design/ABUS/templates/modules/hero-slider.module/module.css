/* Hero Slider Wrapper */
.hero-slider-wrapper {
    width: 100%;
    position: relative;
}

/* Hero Slider Base Styles */
.hero-slider {
    width: 100%;
}

/* Slide Content Styles */
.hero-slide-image,
.hero-slide-video {
    width: 100%;
    overflow: hidden;
}

/* Image Slide Styles - matching hero-image module */
.hero-slide-image img {
    margin: 0 auto;
    max-height: calc(100vh - var(--header-height)) !important;
    width: 100%;
    object-fit: contain;
}

/* Video Slide Styles */
.hero-slide-video {
    position: relative;
    width: 100%;
    height: auto;
}

.hero-slide-video iframe,
.hero-slide-video video {
    width: 100%;
    height: auto;
    max-height: calc(100vh - var(--header-height));
}

/* Splide Navigation Arrows */
.splide__arrow {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.splide__arrow:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}

.splide__arrow:disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.splide__arrow svg {
    width: 24px;
    height: 24px;
}

/* Position arrows */
.splide__arrow--prev {
    left: 20px;
}

.splide__arrow--next {
    right: 20px;
}

/* Splide Pagination */
.splide__pagination {
    bottom: 20px;
    padding: 0;
    margin: 0;
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 8px;
}

.splide__pagination__page {
    background: rgba(255, 255, 255, 0.5);
    border: none;
    border-radius: 50%;
    width: 12px;
    height: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.splide__pagination__page.is-active {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.2);
}

.splide__pagination__page:hover {
    background: rgba(255, 255, 255, 0.8);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .splide__arrow {
        width: 40px;
        height: 40px;
    }

    .splide__arrow svg {
        width: 20px;
        height: 20px;
    }

    .splide__arrow--prev {
        left: 10px;
    }

    .splide__arrow--next {
        right: 10px;
    }

    .splide__pagination {
        bottom: 10px;
    }

    .splide__pagination__page {
        width: 10px;
        height: 10px;
    }
}
