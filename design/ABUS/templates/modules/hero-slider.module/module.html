{% if module.slide %}
<div class="hero-slider-wrapper">
    <div class="splide hero-slider" id="hero-slider-{{ name|replace(' ', '-')|lower }}">
        <div class="splide__track">
            <ul class="splide__list">
                {% for slide in module.slide %}
                    <li class="splide__slide">
                        {% if slide.type == 'image' and slide.hero_image.src %}
                            <div class="hero-slide-image"{% if slide.background_color.color %} style="background-color: rgba({{ slide.background_color.color | convert_rgb }}, {{ slide.background_color.opacity }});"{% endif %}>
                                {% set loadingAttr = slide.hero_image.loading != 'disabled' ? 'loading="{{ slide.hero_image.loading|escape_attr }}"' : '' %}
                                <picture>
                                    {% if slide.hero_image_mobile.src %}
                                        <source srcset="{{ slide.hero_image_mobile.src|escape_url }}" media="(max-width: 767px)">
                                    {% endif %}
                                    <source srcset="{{ slide.hero_image.src|escape_url }}">
                                    <img src="{{ slide.hero_image.src|escape_url }}" alt="{{ slide.hero_image.alt|escape_attr }}" {{ loadingAttr }}>
                                </picture>
                            </div>
                        {% elif slide.type == 'video' and slide.video_player %}
                            <div class="hero-slide-video"{% if slide.background_color.color %} style="background-color: rgba({{ slide.background_color.color | convert_rgb }}, {{ slide.background_color.opacity }});"{% endif %}>
                                {{ slide.video_player }}
                            </div>
                        {% endif %}
                    </li>
                {% endfor %}
            </ul>
        </div>

        <!-- Navigation arrows -->
        <div class="splide__arrows">
            <button class="splide__arrow splide__arrow--prev" aria-label="Previous slide">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
            <button class="splide__arrow splide__arrow--next" aria-label="Next slide">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        </div>

        <!-- Pagination dots -->
        <ul class="splide__pagination"></ul>
    </div>
</div>
{% endif %}
