<div class="hs-membership-social-container">

  {# Facebook login #}

  {% if module.facebook_enabled %}
    <div class="hs-membership-social">

      {{ require_js("https://connect.facebook.net/en_US/sdk.js", "footer") }}
      <div class="hs-facebook-login" data-appid="{{ module.facebook_appid }}"></div>

      <form class="hs-facebook-login-form" method="post" action="/_hcms/mem/social/facebook" style="margin:0px">
        <input class="hs-facebook-access-token" type="hidden" name="accessToken" />
        <input class="hs-facebook-user-id" type="hidden" name="userId" />
        <input type="hidden" name="redirect" value="{{ membership_login_redirect }}" />
      </form>

      <div class="hs-facebook-spinner">
        Loading
        <div
          class="fb-login-button"
          data-max-rows="1"
          data-size="large"
          data-button-type="login_with"
          onlogin="onFacebookLogin()"
          data-scope="email"
        >
        </div>
      </div>

      <script type="text/javascript">
        function onFacebookLogin() {
          FB.getLoginStatus(function(response) {
            if (response.authResponse !== null) {
              document.querySelectorAll('.hs-facebook-user-id').forEach(function(input) {
                input.value = response.authResponse.userID;
              });
              document.querySelectorAll('.hs-facebook-access-token').forEach(function(input) {
                input.value = response.authResponse.accessToken;
              });
            }
            document.getElementsByClassName("hs-facebook-login-form")[0].submit();
          });
        }
      </script>

    </div>
  {% endif %}

  {# Google login #}

  {% if module.google_enabled %}
    {# Cut filter used here in case someone accidentally keeps the full URL provided by Google versus just the client ID #}
    {% set google_client_id = module.google_clientid|cut(".apps.googleusercontent.com") %}

    <div class="hs-membership-social">

      {{ require_js("https://accounts.google.com/gsi/client", { position: "footer", async: true, defer: true }) }}

      <div id="g_id_onload"
        data-client_id="{{ google_client_id }}.apps.googleusercontent.com"
        data-callback="handleCredentialResponse"
        data-auto_prompt="false"
      >
      </div>

      <form class="hs-google-login-form" method="post" action="/_hcms/mem/social/google?clientId={{ google_client_id }}&issuer=https://accounts.google.com">
        <input class="hs-google-id-token" type="hidden" name="googleIdToken" />
        <input type="hidden" name="redirect" value="{{ membership_login_redirect }}" />
      </form>

      <div class="g_id_signin"
        data-type="standard"
        data-text="signin"
        data-width="120"
      >
      </div>

      <script>
        function handleCredentialResponse(CredentialResponse) {
          document.querySelectorAll('.hs-google-id-token').forEach(function(input) {
            input.value = CredentialResponse.credential;
          });
          document.getElementsByClassName("hs-google-login-form")[0].submit();
        }
      </script>

    </div>
  {% endif %}

</div>
