.hs-download__button {
  width: 120px;
  height: 40px;
  padding-left: 12px;
  padding-right: 12px;
  border-radius: 3px;
  font-weight: bold;
  font-size: 12px;
  cursor: pointer;
  border: none;
}

.hs-download__button[disabled] {
  background-color: lightgray;
  cursor: auto;
}

.hs-download__alert--error {
  display: none;
  color: red;
  margin-left: 15px;
}

.hs-download__loading-spinner {
  border: 3px solid white;
  border-radius: 50%;
  border-top-color: black;
  width: 25px;
  height: 25px;
  margin: 0 auto;
  animation: spin 0.5s linear infinite;
  display: inline-block;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@media print {
  .hs-download__button {
    display: none !important;
  }
}
