{# Download a PDF of the quote #}

{% set mock_template_data = {
    "hubspot_base_url" : "https://api.hubapi.com",
    "is_preview": "true",
    "quote" : { "hs_public_url_key": "0123456789abcdef" }
  }
%}
{% set TEMPLATE_DATA = template_data || mock_template_data %}

<span id="hs-download__container">
  <button class="hs-download__button">{{ module.button_text|sanitize_html }}</button>

  <span class="hs-download__alert--error">{{ module.download_error|sanitize_html }}</span>

  <template id="hs-download__template">
    <div class="hs-download__loading-spinner"></div>
  </template>
</span>

{# DO NOT EDIT - used for download #}
{% require_js position="head" %}
<script type="application/json" data-module-instance="download">
  {
    "url_key": "{{ TEMPLATE_DATA.quote.hs_public_url_key|escapejson }}",
    "api_base_url": "{{ TEMPLATE_DATA.hubspot_base_url|escapejson }}",
    "is_preview": {{ TEMPLATE_DATA.is_preview|escapejson }}
  }
</script>
{% end_require_js %}
